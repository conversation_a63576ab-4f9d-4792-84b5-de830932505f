/**
 * Cloud Functions for SIGMA.
 *
 * IMPORTANT TROUBLESHOOTING NOTE:
 * If you encounter a TypeError like "functions.runWith is not a function" or "functions.region is not a function"
 * during `firebase deploy --only functions`, it means the 'firebase-functions' module is not loading
 * correctly in the Firebase CLI analysis environment.
 *
 * This is often caused by:
 * 1. Corrupted node_modules or cache.
 * 2. Conflicts with global npm packages (especially older firebase-tools).
 * 3. Environment issues (like Node.js version mismatch, although we are targeting Node 20).
 *
 * TO ATTEMPT TO FIX THIS ERROR:
 * Please perform the following steps MANUALLY in your terminal:
 *
 * 1. Navigate to the functions directory:
 *    cd firebase/functions
 *
 * 2. Clean up dependencies:
 *    # On Windows Command Prompt (cmd.exe):
 *    rd /s /q node_modules
 *    del package-lock.json
 *
 *    # On Windows PowerShell:
 *    Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue
 *    Remove-Item package-lock.json -ErrorAction SilentlyContinue
 *
 * 3. Reinstall project dependencies:
 *    npm install
 *
 * 4. Update Firebase Tools globally (important!):
 *    # You might need admin privileges for this
 *    npm install -g firebase-tools@latest
 *
 * 5. VERIFY Node.js version in your terminal is LTS (v18 or v20):
 *    node -v
 *    # If not, use NVM (Node Version Manager) to switch: nvm use 20
 *    # (Install NVM if you haven't: https://github.com/coreybutler/nvm-windows)
 *
 * 6. Close and reopen your terminal session.
 *
 * 7. Navigate back to the project root (C:\Users\<USER>\Documents\SIGMA_GAS -AUGMENT>).
 *
 * 8. Attempt deployment again:
 *    firebase deploy --only functions
 *
 * If the error persists after these steps, there might be a deeper environmental issue
 * or a bug report to Firebase support may be necessary.
 */

// The Cloud Functions for Firebase SDK to create Cloud Functions and set up triggers.
// Importing v1 API specifically to ensure compatibility with runWith and region methods
const functions = require("firebase-functions/v1");
// The Firebase Admin SDK to access Firestore.
const admin = require("firebase-admin");
// Logger for structured logging
const logger = require("firebase-functions/logger");

// Initialize Firebase Admin SDK
try {
  admin.initializeApp();
  logger.info("Firebase Admin SDK initialized successfully.");
} catch (error) {
  logger.error("Error initializing Firebase Admin SDK:", error);
}

// Simple test function
exports.helloWorld = functions.runWith({ region: 'europe-west1' }).https.onCall((data, context) => {
  logger.info("Hello world function called");
  return { message: "Hello from Firebase!" };
});

// Import user management business logic functions
const userManagementLogic = require('./userManagement');
// Import auth trigger logic functions
const authTriggerLogic = require('./authTriggers');
// Import emprunts business logic functions
const empruntsLogic = require('./emprunts');

// Export callable functions for user management with proper error handling
// Each function is wrapped with the Firebase Functions SDK and specifies the region
// We group them under the userManagement namespace to match the client-side expectations

// Create the userManagement namespace
exports.userManagement = {};

// Define setUserRole function
exports.userManagement.setUserRole = functions.runWith({ region: 'europe-west1' }).https.onCall(async (data, context) => {
  try {
    // Call the business logic function
    return await userManagementLogic.setUserRoleLogic(data, context);
  } catch (error) {
    // Log the error if it's not already a HttpsError
    if (!(error instanceof functions.https.HttpsError)) {
      logger.error(`Unhandled error in setUserRole: ${error.message}`, {
        stack: error.stack,
        data,
        contextAuth: context.auth
      });
    }
    // Re-throw the error for Firebase to handle
    throw error;
  }
});

// Define getUserRole function
exports.userManagement.getUserRole = functions.runWith({ region: 'europe-west1' }).https.onCall(async (data, context) => {
  try {
    // Call the business logic function
    return await userManagementLogic.getUserRoleLogic(data, context);
  } catch (error) {
    // Log the error if it's not already a HttpsError
    if (!(error instanceof functions.https.HttpsError)) {
      logger.error(`Unhandled error in getUserRole: ${error.message}`, {
        stack: error.stack,
        data,
        contextAuth: context.auth
      });
    }
    // Re-throw the error for Firebase to handle
    throw error;
  }
});

// Define migrateUserRoles function
exports.userManagement.migrateUserRoles = functions.runWith({ region: 'europe-west1' }).https.onCall(async (data, context) => {
  try {
    // Call the business logic function
    return await userManagementLogic.migrateUserRolesLogic(data, context);
  } catch (error) {
    // Log the error if it's not already a HttpsError
    if (!(error instanceof functions.https.HttpsError)) {
      logger.error(`Unhandled error in migrateUserRoles: ${error.message}`, {
        stack: error.stack,
        data,
        contextAuth: context.auth
      });
    }
    // Re-throw the error for Firebase to handle
    throw error;
  }
});

// Define listAuthUsers function
exports.userManagement.listAuthUsers = functions.runWith({ region: 'europe-west1' }).https.onCall(async (data, context) => {
  try {
    // Call the business logic function
    return await userManagementLogic.listAuthUsersLogic(data, context);
  } catch (error) {
    // Log the error if it's not already a HttpsError
    if (!(error instanceof functions.https.HttpsError)) {
      logger.error(`Unhandled error in listAuthUsers: ${error.message}`, {
        stack: error.stack,
        data,
        contextAuth: context.auth
      });
    }
    // Re-throw the error for Firebase to handle
    throw error;
  }
});

// Auth trigger for new user creation
// Use the handleUserCreate function from authTriggers.js to avoid deployment issues
exports.onUserCreated = functions.runWith({ region: 'europe-west1' }).auth.user().onCreate(authTriggerLogic.handleUserCreate);

// Export callable functions for emprunts management
// Create the emprunts namespace
exports.emprunts = {};

// Define createEmprunt function
exports.emprunts.createEmprunt = functions.runWith({ region: 'europe-west1' }).https.onCall(async (data, context) => {
  try {
    // Call the business logic function
    return await empruntsLogic.createEmpruntLogic(data, context);
  } catch (error) {
    // Log the error if it's not already a HttpsError
    if (!(error instanceof functions.https.HttpsError)) {
      logger.error(`Unhandled error in createEmprunt: ${error.message}`, {
        stack: error.stack,
        data,
        contextAuth: context.auth
      });
    }
    // Re-throw the error for Firebase to handle
    throw error;
  }
});
