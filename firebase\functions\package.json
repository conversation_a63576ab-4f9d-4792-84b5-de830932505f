{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1"}, "devDependencies": {"@firebase/rules-unit-testing": "^2.0.0", "@types/jest": "^29.5.14", "firebase": "^9.23.0", "firebase-functions-test": "^3.1.0", "jest": "^29.7.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "private": true}