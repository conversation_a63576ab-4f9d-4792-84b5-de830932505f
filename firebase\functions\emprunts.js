/**
 * Logique métier pour les emprunts dans SIGMA
 * 
 * Ce fichier contient les fonctions de logique métier pour la gestion des emprunts.
 * Ces fonctions sont appelées par les Cloud Functions définies dans index.js.
 */

const admin = require("firebase-admin");
const functions = require("firebase-functions/v1");
const logger = require("firebase-functions/logger");

/**
 * Logique métier pour créer un emprunt
 * @param {Object} data - Données de l'emprunt à créer
 * @param {Object} context - Contexte de la fonction (auth, etc.)
 * @returns {Promise<Object>} - Résultat de la création
 */
async function createEmpruntLogic(data, context) {
  // Vérification de l'authentification
  if (!context.auth) {
    logger.warn("Tentative d'appel non authentifié à createEmprunt");
    throw new functions.https.HttpsError(
      'unauthenticated',
      'Vous devez être connecté pour créer un emprunt.'
    );
  }

  // Vérification des permissions - seuls admin et regisseur peuvent créer des emprunts
  const userRole = context.auth.token.role;
  if (!userRole || (userRole !== 'admin' && userRole !== 'regisseur')) {
    logger.warn(`Tentative de création d'emprunt par un utilisateur non autorisé: ${context.auth.uid}, rôle: ${userRole}`);
    throw new functions.https.HttpsError(
      'permission-denied',
      'Seuls les administrateurs et régisseurs peuvent créer des emprunts.'
    );
  }

  // Validation des données d'entrée
  if (!data || typeof data !== 'object') {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Les données de l\'emprunt sont requises.'
    );
  }

  // Validation des champs obligatoires et de leur contenu
  const requiredFields = ['nom', 'lieu', 'dateDepart', 'dateRetour', 'secteur', 'emprunteur'];
  const missingFields = requiredFields.filter(field => !data[field]);

  if (missingFields.length > 0) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      `Champs obligatoires manquants: ${missingFields.join(', ')}`
    );
  }

  // Validation des types de données et contenu
  if (typeof data.nom !== 'string' || data.nom.trim().length === 0) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Le nom de la manipulation doit être une chaîne non vide.'
    );
  }

  if (typeof data.lieu !== 'string' || data.lieu.trim().length === 0) {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Le lieu doit être une chaîne non vide.'
    );
  }

  if (typeof data.secteur !== 'string') {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Le secteur doit être une chaîne.'
    );
  }

  if (typeof data.emprunteur !== 'string') {
    throw new functions.https.HttpsError(
      'invalid-argument',
      'L\'emprunteur doit être une chaîne.'
    );
  }

  // Validation des dates
  let dateDepart, dateRetour;
  try {
    // Les dates peuvent être des timestamps Firestore, des objets Date, ou des objets avec seconds
    if (data.dateDepart && typeof data.dateDepart === 'object' && data.dateDepart.seconds) {
      // Mock timestamp pour les tests
      dateDepart = new Date(data.dateDepart.seconds * 1000);
    } else if (data.dateDepart instanceof admin.firestore.Timestamp) {
      dateDepart = data.dateDepart.toDate();
    } else {
      dateDepart = new Date(data.dateDepart);
    }

    if (data.dateRetour && typeof data.dateRetour === 'object' && data.dateRetour.seconds) {
      // Mock timestamp pour les tests
      dateRetour = new Date(data.dateRetour.seconds * 1000);
    } else if (data.dateRetour instanceof admin.firestore.Timestamp) {
      dateRetour = data.dateRetour.toDate();
    } else {
      dateRetour = new Date(data.dateRetour);
    }

    if (isNaN(dateDepart.getTime()) || isNaN(dateRetour.getTime())) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Les dates de départ et de retour doivent être valides.'
      );
    }

    if (dateRetour <= dateDepart) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'La date de retour doit être postérieure à la date de départ.'
      );
    }
  } catch (error) {
    // Si c'est déjà une HttpsError, la relancer
    if (error instanceof functions.https.HttpsError) {
      throw error;
    }
    // Sinon, créer une nouvelle HttpsError
    throw new functions.https.HttpsError(
      'invalid-argument',
      'Les dates de départ et de retour doivent être valides.'
    );
  }

  // Préparer les données de l'emprunt
  const now = admin.firestore.Timestamp.now();
  const empruntData = {
    nom: data.nom.trim(),
    lieu: data.lieu.trim(),
    dateDepart: admin.firestore.Timestamp.fromDate(dateDepart),
    dateRetour: admin.firestore.Timestamp.fromDate(dateRetour),
    secteur: data.secteur.trim(),
    emprunteur: data.emprunteur.trim(),
    referent: data.referent ? data.referent.trim() : '',
    notes: data.notes ? data.notes.trim() : '',
    statut: 'draft', // Statut initial selon la documentation
    estInventorie: false,
    estFacture: false,
    createdBy: context.auth.uid,
    createdAt: now,
    updatedAt: now
  };

  const db = admin.firestore();

  try {
    // Utiliser une transaction pour garantir l'atomicité
    const result = await db.runTransaction(async (transaction) => {
      // Créer une nouvelle référence de document
      const empruntRef = db.collection('emprunts').doc();
      
      // Créer l'emprunt dans la transaction
      transaction.set(empruntRef, empruntData);

      // Créer une entrée dans l'historique
      const historiqueRef = empruntRef.collection('historique').doc();
      const historiqueData = {
        date: now,
        action: 'creation',
        utilisateur: context.auth.uid,
        notes: 'Emprunt créé'
      };
      transaction.set(historiqueRef, historiqueData);

      return {
        id: empruntRef.id,
        ...empruntData
      };
    });

    logger.info(`Emprunt créé avec succès: ${result.id}`, {
      empruntId: result.id,
      createdBy: context.auth.uid,
      nom: empruntData.nom
    });

    return {
      success: true,
      emprunt: result,
      message: 'Emprunt créé avec succès'
    };

  } catch (error) {
    logger.error('Erreur lors de la création de l\'emprunt:', {
      error: error.message,
      stack: error.stack,
      userId: context.auth.uid,
      data: data
    });

    throw new functions.https.HttpsError(
      'internal',
      'Erreur lors de la création de l\'emprunt. Veuillez réessayer.'
    );
  }
}

module.exports = {
  createEmpruntLogic
};
